import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { CreateUserModal } from './CreateUserModal';
import { EditUserModal } from './EditUserModal';
import { ConfirmationDialog } from './ConfirmationDialog';

// Mock user data for testing
const mockUser = {
  id: '1',
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  phone: '+**********',
  role: 'member',
  status: 'active',
  company: 'TechCorp Solutions',
  subscription: 'Premium Health Plan',
  lastActive: '2 hours ago',
  createdAt: '2024-01-15T00:00:00Z',
  updatedAt: '2024-01-15T00:00:00Z',
};

const mockCompanies = [
  'TechCorp Solutions',
  'Global Health Corp',
  'WellnessTech Inc',
  'Mindfulness Center',
  'InnovateHealth Corp',
];

export function UserManagementTest() {
  const [createModalOpen, setCreateModalOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);

  const handleCreateUser = async (userData: any): Promise<boolean> => {
    console.log('Creating user:', userData);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    alert('User created successfully!');
    return true;
  };

  const handleUpdateUser = async (id: string, userData: any): Promise<boolean> => {
    console.log('Updating user:', id, userData);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    alert('User updated successfully!');
    return true;
  };

  const handleConfirmAction = async () => {
    console.log('Confirmed action');
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    alert('Action completed!');
  };

  return (
    <div className="p-8 space-y-4">
      <h1 className="text-2xl font-bold">User Management Modal Tests</h1>
      
      <div className="space-x-4">
        <Button onClick={() => setCreateModalOpen(true)}>
          Test Create User Modal
        </Button>
        
        <Button onClick={() => setEditModalOpen(true)}>
          Test Edit User Modal
        </Button>
        
        <Button onClick={() => setConfirmDialogOpen(true)}>
          Test Confirmation Dialog
        </Button>
      </div>

      <div className="mt-8">
        <h2 className="text-lg font-semibold mb-2">Modal States:</h2>
        <ul className="space-y-1 text-sm">
          <li>Create Modal: {createModalOpen ? 'Open' : 'Closed'}</li>
          <li>Edit Modal: {editModalOpen ? 'Open' : 'Closed'}</li>
          <li>Confirm Dialog: {confirmDialogOpen ? 'Open' : 'Closed'}</li>
        </ul>
      </div>

      {/* Modals */}
      <CreateUserModal
        open={createModalOpen}
        onOpenChange={setCreateModalOpen}
        onCreateUser={handleCreateUser}
        companies={mockCompanies}
      />

      <EditUserModal
        open={editModalOpen}
        onOpenChange={setEditModalOpen}
        onUpdateUser={handleUpdateUser}
        user={mockUser}
        companies={mockCompanies}
      />

      <ConfirmationDialog
        open={confirmDialogOpen}
        onOpenChange={setConfirmDialogOpen}
        action="delete"
        userName="John Doe"
        userRole="member"
        onConfirm={handleConfirmAction}
        isLoading={false}
      />
    </div>
  );
}
