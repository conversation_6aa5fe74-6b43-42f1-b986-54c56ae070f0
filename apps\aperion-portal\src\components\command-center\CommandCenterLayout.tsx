import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Outlet } from 'react-router-dom';
import {
  Shield,
  Users,
  Building,
  Lock,
  BarChart3,
  Activity,
  Eye,
  Settings,
  Menu,
  X,
  LogOut,
  Home,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useCommandCenterToastContext, CommandCenterToastContainer, CommandCenterToastProvider } from './CommandCenterToast';
import { useAuth } from '@/contexts/AuthContext';

interface CommandCenterLayoutProps {
  children: React.ReactNode;
}

interface SidebarItem {
  id: string;
  label: string;
  href: string;
  icon: React.ReactNode;
  badge?: string;
}

interface SidebarGroup {
  category: string;
  items: SidebarItem[];
}

function CommandCenterLayoutContent({ children }: CommandCenterLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const location = useLocation();
  const commandCenterToast = useCommandCenterToastContext();
  const { user, logout } = useAuth();

  const sidebarGroups: SidebarGroup[] = [
    {
      category: 'Overview',
      items: [
        {
          id: 'overview',
          label: 'Dashboard',
          href: '/admin/dashboard',
          icon: <Shield className="w-5 h-5" />,
        },
        // {
        //   id: 'monitoring',
        //   label: 'Service Monitoring',
        //   href: '/command-center/monitoring',
        //   icon: <Activity className="w-5 h-5" />,
        // },
      ],
    },
    {
      category: 'User Management',
      items: [
        {
          id: 'users',
          label: 'Users',
          href: '/admin/users',
          icon: <Users className="w-5 h-5" />,
          badge: '12.4K',
        },
        // {
        //   id: 'sessions',
        //   label: 'Sessions',
        //   href: '/command-center/sessions',
        //   icon: <Eye className="w-5 h-5" />,
        // },
      ],
    },
    // {
    //   category: 'Business Operations',
    //   items: [
    //     {
    //       id: 'subscriptions',
    //       label: 'Subscriptions',
    //       href: '/command-center/subscriptions',
    //       icon: <Building className="w-5 h-5" />,
    //       badge: '847',
    //     },
    //     {
    //       id: 'analytics',
    //       label: 'Analytics',
    //       href: '/command-center/analytics',
    //       icon: <BarChart3 className="w-5 h-5" />,
    //     },
    //   ],
    // },
    // {
    //   category: 'Security & Settings',
    //   items: [
    //     {
    //       id: 'security',
    //       label: 'Security Center',
    //       href: '/command-center/security',
    //       icon: <Lock className="w-5 h-5" />,
    //       badge: '3',
    //     },
    //     {
    //       id: 'settings',
    //       label: 'Platform Settings',
    //       href: '/command-center/settings',
    //       icon: <Settings className="w-5 h-5" />,
    //     },
    //   ],
    // },
  ];

  const isActive = (href: string) => {
    if (href === '/command-center') {
      return location.pathname === href;
    }
    return location.pathname.startsWith(href);
  };

  const toggleSidebar = () => setSidebarOpen(!sidebarOpen);

  const handleLogout = () => {
    commandCenterToast.success('Logout Successful', 'You have been successfully logged out.');
    logout();
  };

  return (
    <div className="min-h-screen h-screen bg-slate-50  dark:bg-slate-900 flex overflow-y-hidden">
      <div
        className=" bg-white border border-slate-200 dark:border-slate-700"
        style={{
          scrollbarWidth: 'thin', // For Firefox (but not exactly 1px)
          msOverflowStyle: 'none', // IE/Edge fallback
        }}
      >
        {/* Sidebar */}
        <motion.aside
          initial={false}
          animate={{
            width: sidebarOpen ? 280 : 72,
          }}
          transition={{ duration: 0.2, ease: 'easeInOut' }}
          className="bg-white dark:bg-slate-800 border-r  dark:border-slate-700 flex flex-col relative"
        >
          {/* Sidebar Header */}
          <div className="h-16 flex items-center justify-between px-4 border-b dark:border-slate-700">
            <AnimatePresence mode="wait">
              {sidebarOpen && (
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.2 }}
                  className="flex items-center space-x-2"
                >
                  <Shield className="w-8 h-8 text-blue-600" />
                  <div>
                    <h1 className="text-lg font-bold text-slate-900 dark:text-slate-100">
                      Command Center
                    </h1>
                    <p className="text-xs text-slate-500 dark:text-slate-400">
                      Administrative Hub
                    </p>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            <Button
              variant="ghost"
              size="sm"
              onClick={toggleSidebar}
              className="h-8 w-8 p-0 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200"
            >
              {sidebarOpen ? (
                <X className="w-4 h-4" />
              ) : (
                <Menu className="w-4 h-4" />
              )}
            </Button>
          </div>

          {/* Sidebar Navigation */}
          <div className="flex-1 overflow-y-auto overflow-x-hidden">
            <div className="p-3 space-y-4">
              {sidebarGroups.map(group => (
                <div key={group.category} className="space-y-1">
                  {sidebarOpen && (
                    <h3 className="px-3 text-xs font-semibold text-slate-500 dark:text-slate-400 uppercase tracking-wider">
                      {group.category}
                    </h3>
                  )}
                  {group.items.map(item => (
                    <div key={item.id}>
                      <Link to={item.href}>
                        <div
                          className={`
                        group relative flex items-center p-3 rounded-lg transition-colors duration-150 cursor-pointer
                        ${
                          isActive(item.href)
                            ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                            : 'text-slate-600 dark:text-slate-400 hover:bg-slate-50 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-200'
                        }
                      `}
                        >
                          <div className="flex-shrink-0">{item.icon}</div>
                          {/* Scoped scrollbar styling */}
                          <style>
                            {`
          div::-webkit-scrollbar {
            width: 8px;
          }

          div::-webkit-scrollbar-thumb {
            background-color: #3b82f6; /* blue-500 */
            border-radius: 8px;
          }

          div::-webkit-scrollbar-track {
            background-color: #e0f2fe; /* light blue */
          }

          /* Firefox support */
          div {
            scrollbar-width: thin;
            scrollbar-color: #3b82f6 #e0f2fe;
          }
        `}
                          </style>

                          <AnimatePresence mode="wait">
                            {sidebarOpen && (
                              <motion.div
                                initial={{ opacity: 0, x: -10 }}
                                animate={{ opacity: 1, x: 0 }}
                                exit={{ opacity: 0, x: -10 }}
                                transition={{ duration: 0.15 }}
                                className="flex-1 ml-3 min-w-0"
                              >
                                <div className="flex items-center justify-between">
                                  <p className="text-sm font-medium truncate">
                                    {item.label}
                                  </p>
                                  {item.badge && (
                                    <span className="ml-2 bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded-full dark:bg-blue-900/50 dark:text-blue-300">
                                      {item.badge}
                                    </span>
                                  )}
                                </div>
                              </motion.div>
                            )}
                          </AnimatePresence>

                          {isActive(item.href) && (
                            <div className="absolute right-0 top-1/2 w-1 h-8 bg-blue-500 rounded-l-full transform -translate-y-1/2" />
                          )}
                        </div>
                      </Link>
                    </div>
                  ))}
                </div>
              ))}
            </div>
          </div>

          {/* Footer with actions - positioned at bottom */}
          {user && (
            <div className="flex-shrink-0 p-3 border-t border-slate-200 dark:border-slate-700 space-y-1">
              <div
                className="group relative flex items-center p-3 rounded-lg transition-colors duration-150 cursor-pointer text-slate-600 dark:text-slate-400 hover:bg-slate-50 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-200"
                onClick={() => window.location.href = '/'}
              >
                <div className="flex-shrink-0">
                  <Home className="w-5 h-5" />
                </div>
                <AnimatePresence mode="wait">
                  {sidebarOpen && (
                    <motion.div
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -10 }}
                      transition={{ duration: 0.15 }}
                      className="flex-1 ml-3 min-w-0"
                    >
                      <p className="text-sm font-medium truncate">Back to Home</p>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              <div
                className="group relative flex items-center p-3 rounded-lg transition-colors duration-150 cursor-pointer text-slate-600 dark:text-slate-400 hover:bg-slate-50 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-200"
                onClick={handleLogout}
              >
                <div className="flex-shrink-0">
                  <LogOut className="w-5 h-5" />
                </div>
                <AnimatePresence mode="wait">
                  {sidebarOpen && (
                    <motion.div
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -10 }}
                      transition={{ duration: 0.15 }}
                      className="flex-1 ml-3 min-w-0"
                    >
                      <p className="text-sm font-medium truncate">Logout</p>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          )}
        </motion.aside>
      </div>

      {/* Mobile Sidebar Overlay */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={toggleSidebar}
            className="fixed inset-0 -z-10 bg-black/30 lg:hidden"
          />
        )}
      </AnimatePresence>

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Mobile Header */}
        <div className="lg:hidden h-16 bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 flex items-center justify-between px-4">
          <div className="flex items-center space-x-2">
            <Shield className="w-6 h-6 text-blue-600" />
            <h1 className="text-lg font-bold text-slate-900 dark:text-slate-100">
              Command Center
            </h1>
          </div>
          {/* <Button
            variant="ghost"
            size="sm"
            onClick={toggleSidebar}
            className="h-8 w-8 p-0"
          >
            <Menu className="w-4 h-4" />
          </Button> */}
        </div>

        {/* Page Content */}
        <main className="flex-1 overflow-hidden">
          {/* Temporary test button for debugging */}
          {process.env.NODE_ENV === 'development' && (
            <div className="fixed bottom-4 right-4 z-[300]">
              <Button
                onClick={() => {
                  console.log('Test button clicked - triggering toast');
                  commandCenterToast.success('Layout Test', 'Toast from CommandCenterLayout');
                }}
                className="bg-purple-500 hover:bg-purple-600 text-white"
              >
                Test Layout Toast
              </Button>
            </div>
          )}
          {children || <Outlet />}
        </main>
      </div>

      {/* Toast Container */}
      <CommandCenterToastContainer
        toasts={commandCenterToast.toasts}
        onClose={commandCenterToast.removeToast}
      />
    </div>
  );
}

// Main layout component with provider
export function CommandCenterLayout({ children }: CommandCenterLayoutProps) {
  return (
    <CommandCenterToastProvider>
      <CommandCenterLayoutContent>{children}</CommandCenterLayoutContent>
    </CommandCenterToastProvider>
  );
}
