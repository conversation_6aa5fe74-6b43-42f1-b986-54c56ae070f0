import { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Shield, CheckCircle, AlertTriangle, X, Settings, Users, Database } from 'lucide-react';
import { Button } from '@/components/ui/button';

export interface CommandCenterToast {
  id: string;
  title: string;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  action?: string;
  duration?: number;
}

export interface CommandCenterToastContainerProps {
  toasts: CommandCenterToast[];
  onClose: (id: string) => void;
}

export function CommandCenterToastContainer({ toasts, onClose }: CommandCenterToastContainerProps) {
  // Debug logging for toast rendering
  console.log('CommandCenterToastContainer render - toasts count:', toasts.length);
  console.log('CommandCenterToastContainer render - toasts:', toasts);

  return (
    <div className="fixed top-4 left-4 z-[200] space-y-2 max-w-sm w-full pointer-events-none">
      <AnimatePresence>
        {toasts.map((toast) => (
          <CommandCenterToastItem
            key={toast.id}
            toast={toast}
            onClose={() => onClose(toast.id)}
          />
        ))}
      </AnimatePresence>
    </div>
  );
}

function CommandCenterToastItem({ toast, onClose }: { toast: CommandCenterToast; onClose: () => void }) {
  const getToastStyles = () => {
    switch (toast.type) {
      case 'success':
        return {
          bg: 'bg-gradient-to-r from-green-50 to-slate-100 dark:from-green-900/20 dark:to-slate-700',
          border: 'border-green-200 dark:border-green-600',
          icon: <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />,
          accent: 'border-l-4 border-l-green-500'
        };
      case 'error':
        return {
          bg: 'bg-gradient-to-r from-red-50 to-slate-50 dark:from-red-900/20 dark:to-slate-800',
          border: 'border-red-200 dark:border-red-800',
          icon: <AlertTriangle className="w-5 h-5 text-red-600 dark:text-red-400" />,
          accent: 'border-l-4 border-l-red-500'
        };
      case 'warning':
        return {
          bg: 'bg-gradient-to-r from-amber-50 to-slate-50 dark:from-amber-900/20 dark:to-slate-800',
          border: 'border-amber-200 dark:border-amber-800',
          icon: <AlertTriangle className="w-5 h-5 text-amber-600 dark:text-amber-400" />,
          accent: 'border-l-4 border-l-amber-500'
        };
      default:
        return {
          bg: 'bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-700',
          border: 'border-slate-200 dark:border-slate-600',
          icon: <Settings className="w-5 h-5 text-slate-600 dark:text-slate-300" />,
          accent: 'border-l-4 border-l-slate-500'
        };
    }
  };

  const styles = getToastStyles();

  return (
    <motion.div
      initial={{ opacity: 0, x: -300, scale: 0.8 }}
      animate={{ opacity: 1, x: 0, scale: 1 }}
      exit={{ opacity: 0, x: -300, scale: 0.8 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      className={`${styles.bg} ${styles.border} ${styles.accent} rounded-lg shadow-xl p-4 backdrop-blur-sm pointer-events-auto border-2`}
    >
      <div className="flex items-start space-x-3">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.1, type: "spring", stiffness: 200 }}
        >
          {styles.icon}
        </motion.div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-1">
              <Shield className="w-4 h-4 text-slate-500 dark:text-slate-400" />
              <h4 className="text-sm font-semibold text-slate-900 dark:text-slate-100">
                {toast.title}
              </h4>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-6 w-6 p-0 text-slate-400 hover:text-slate-600 dark:text-slate-500 dark:hover:text-slate-300"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
          
          <p className="text-sm text-slate-600 dark:text-slate-300 mt-1">
            {toast.message}
          </p>
          
          {toast.action && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="mt-2"
            >
              <span className="text-xs text-slate-500 dark:text-slate-400 bg-slate-100 dark:bg-slate-700 px-2 py-1 rounded">
                {toast.action}
              </span>
            </motion.div>
          )}
        </div>
      </div>
    </motion.div>
  );
}

export function useCommandCenterToast() {
  const [toasts, setToasts] = useState<CommandCenterToast[]>([]);

  const addToast = useCallback((toast: Omit<CommandCenterToast, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newToast = { ...toast, id };

    // Debug logging for toast creation
    console.log('CommandCenterToast - Adding toast:', newToast);

    setToasts(prev => {
      const updated = [...prev, newToast];
      console.log('CommandCenterToast - Updated toasts array:', updated);
      return updated;
    });

    // Auto remove after duration
    setTimeout(() => {
      console.log('CommandCenterToast - Auto-removing toast:', id);
      setToasts(prev => prev.filter(t => t.id !== id));
    }, toast.duration || 5000);
  }, []);

  const removeToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(t => t.id !== id));
  }, []);

  const commandCenterToast = {
    // System management actions
    systemUpdated: () => addToast({
      type: 'success',
      title: 'System Updated',
      message: 'Platform configuration has been successfully updated.',
      action: 'System Management'
    }),

    // User management actions
    userCreated: (username: string) => addToast({
      type: 'success',
      title: 'User Created',
      message: `User "${username}" has been successfully created.`,
      action: 'User Management'
    }),

    userUpdated: (username: string) => addToast({
      type: 'success',
      title: 'User Updated',
      message: `User "${username}" profile has been updated.`,
      action: 'User Management'
    }),

    userSuspended: (username: string) => addToast({
      type: 'warning',
      title: 'User Suspended',
      message: `User "${username}" has been suspended from the platform.`,
      action: 'Security Action'
    }),

    userDeleted: (username: string) => addToast({
      type: 'error',
      title: 'User Deleted',
      message: `User "${username}" has been permanently deleted from the platform.`,
      action: 'User Management'
    }),

    // Subscription management
    subscriptionCreated: (employer: string) => addToast({
      type: 'success',
      title: 'Subscription Created',
      message: `New subscription created for "${employer}".`,
      action: 'Subscription Management'
    }),

    subscriptionUpdated: (employer: string) => addToast({
      type: 'success',
      title: 'Subscription Updated',
      message: `Subscription for "${employer}" has been updated.`,
      action: 'Subscription Management'
    }),

    // Security and monitoring
    securityAlert: (message: string) => addToast({
      type: 'error',
      title: 'Security Alert',
      message: message,
      action: 'Security Monitoring',
      duration: 8000
    }),

    auditLogCreated: () => addToast({
      type: 'info',
      title: 'Audit Log',
      message: 'Security action has been logged for review.',
      action: 'Audit Trail'
    }),

    // Service monitoring
    serviceDown: (serviceName: string) => addToast({
      type: 'error',
      title: 'Service Alert',
      message: `${serviceName} is currently experiencing issues.`,
      action: 'Service Monitoring',
      duration: 10000
    }),

    serviceRestored: (serviceName: string) => addToast({
      type: 'success',
      title: 'Service Restored',
      message: `${serviceName} is now operating normally.`,
      action: 'Service Monitoring'
    }),

    // Analytics and insights
    reportGenerated: (reportType: string) => addToast({
      type: 'success',
      title: 'Report Generated',
      message: `${reportType} report has been generated successfully.`,
      action: 'Analytics'
    }),

    // General actions
    success: (title: string, message: string) => addToast({
      type: 'success',
      title,
      message,
      action: 'Command Center'
    }),

    error: (title: string, message: string) => addToast({
      type: 'error',
      title,
      message,
      action: 'Command Center'
    }),

    warning: (title: string, message: string) => addToast({
      type: 'warning',
      title,
      message,
      action: 'Command Center'
    }),

    info: (title: string, message: string) => addToast({
      type: 'info',
      title,
      message,
      action: 'Command Center'
    }),

    toasts,
    removeToast
  };

  return commandCenterToast;
}